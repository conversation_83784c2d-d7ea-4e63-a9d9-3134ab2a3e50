"use client";

import { useState, useEffect } from 'react';

export default function TestPermissionPage() {
  const [authStatus, setAuthStatus] = useState("Testing...");

  useEffect(() => {
    const testAuth = async () => {
      try {
        console.log('🔍 Testing /api/auth/me...');
        const response = await fetch('/api/auth/me');
        console.log('📡 Response status:', response.status);
        console.log('📡 Response ok:', response.ok);

        const data = await response.json();
        console.log('📊 Response data:', data);

        if (response.ok && data.success) {
          setAuthStatus(`Logged in as: ${data.user.email}`);
        } else {
          setAuthStatus("Not logged in (expected)");
        }
      } catch (error) {
        console.error('❌ Auth test error:', error);
        setAuthStatus(`Error: ${error}`);
      }
    };

    testAuth();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Auth API Test</h1>

      <div className="mb-4">
        <strong>Auth Status:</strong> {authStatus}
      </div>

      <div className="mt-4">
        <a href="/api/auth/me" target="_blank" className="text-blue-600 underline">
          Test /api/auth/me directly
        </a>
      </div>

      <div className="mt-4">
        <a href="/api/config/databases" target="_blank" className="text-blue-600 underline">
          Test /api/config/databases directly
        </a>
      </div>

      <div className="mt-4">
        <a href="/data/list/us_pmn" className="text-blue-600 underline">
          Test us_pmn database
        </a>
      </div>
    </div>
  );
}
